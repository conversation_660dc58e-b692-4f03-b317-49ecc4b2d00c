# Maitrix Auto Bot Configuration
# Copy this file to .env and fill in your values

# Wallet Configuration (Required)
PRIVATE_KEY=your_private_key_1_here
PRIVATE_KEY_2=your_private_key_2_here

# Network Configuration (Required)
RPC_URL=https://sepolia-rollup.arbitrum.io/rpc

# Auto Mode Configuration (Optional)
AUTO_MODE=true

# Token Addresses (Pre-configured for Maitrix Testnet)
ATH_ADDRESS=******************************************
AI16Z_ADDRESS=******************************************
USDE_ADDRESS=******************************************
VANA_ADDRESS=******************************************
VIRTUAL_ADDRESS=******************************************
LULUSD_ADDRESS=******************************************
AZUSD_ADDRESS=******************************************
VANAUSD_ADDRESS=******************************************
AUSD_ADDRESS=******************************************
VUSD_ADDRESS=******************************************

# Instructions:
# 1. Replace 'your_private_key_1_here' with your first wallet's private key
# 2. Replace 'your_private_key_2_here' with your second wallet's private key
# 3. Set AUTO_MODE=true for automatic 24H operation
# 4. Set AUTO_MODE=false for manual CLI control
# 5. Save this file as '.env' (remove .example extension)
