#!/bin/bash

# Script untuk mengubah bot men<PERSON><PERSON> 100% full balance
echo "🚀 Applying Full Balance Patch..."
echo "=================================="

# Check if index.js exists
if [ ! -f "index.js" ]; then
    echo "❌ index.js not found!"
    exit 1
fi

# Backup original file
echo "📦 Creating backup..."
cp index.js index.js.backup
echo "✅ Backup created: index.js.backup"

# Apply patch using sed
echo "🔧 Applying full balance patch..."

# Replace getOptimalMintAmount function
sed -i '/async function getOptimalMintAmount/,/^}/c\
async function getOptimalMintAmount(inputTokenAddress, minAmount, decimals) {\
  try {\
    const contract = new ethers.Contract(inputTokenAddress, ERC20ABI, globalWallet);\
    const balance = await contract.balanceOf(globalWallet.address);\
    const balanceFormatted = ethers.formatUnits(balance, decimals);\
    \
    // Convert to number for calculation\
    const balanceNum = parseFloat(balanceFormatted);\
    \
    // Check if we have minimum required\
    if (balanceNum < minAmount) {\
      addLog(`⚠️ Balance ${balanceNum} < minimum ${minAmount} untuk mint`, "warning");\
      return null;\
    }\
    \
    // Use 100% of balance - FULL BALANCE!\
    const fullAmount = balanceNum;\
    \
    addLog(`💰 MINT: Using FULL balance ${fullAmount} (100%)`, "system");\
    return fullAmount;\
    \
  } catch (error) {\
    addLog(`Error getting full balance for ${inputTokenAddress}: ${error.message}`, "error");\
    return null;\
  }\
}' index.js

# Replace getOptimalStakeAmount function
sed -i '/async function getOptimalStakeAmount/,/^}/c\
async function getOptimalStakeAmount(tokenAddress, minAmount, decimals) {\
  try {\
    const contract = new ethers.Contract(tokenAddress, ERC20ABI, globalWallet);\
    const balance = await contract.balanceOf(globalWallet.address);\
    const balanceFormatted = ethers.formatUnits(balance, decimals);\
    \
    // Convert to number for calculation\
    const balanceNum = parseFloat(balanceFormatted);\
    \
    // Check if we have minimum required\
    if (balanceNum < minAmount) {\
      addLog(`⚠️ Balance ${balanceNum} < minimum ${minAmount} untuk stake`, "warning");\
      return null;\
    }\
    \
    // Use 100% of balance - FULL BALANCE!\
    const fullAmount = balanceNum;\
    \
    addLog(`💰 STAKE: Using FULL balance ${fullAmount} (100%)`, "system");\
    return fullAmount;\
    \
  } catch (error) {\
    addLog(`Error getting full balance for ${tokenAddress}: ${error.message}`, "error");\
    return null;\
  }\
}' index.js

# Update log messages
sed -i 's/99\.9%/100%/g' index.js
sed -i 's/99\.8%/100%/g' index.js
sed -i 's/Optimal=/Full=/g' index.js
sed -i 's/optimalAmount/fullAmount/g' index.js

echo "✅ Patch applied successfully!"
echo ""
echo "📊 Changes made:"
echo "- getOptimalMintAmount: Now uses 100% balance"
echo "- getOptimalStakeAmount: Now uses 100% balance"
echo "- All log messages updated to reflect 100% usage"
echo ""
echo "⚠️  Important:"
echo "- Bot will now use 100% of available tokens"
echo "- Make sure you have enough ETH for gas fees"
echo "- Test with small amounts first"
echo ""
echo "🚀 Ready to use! Run: npm start"
