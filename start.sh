#!/bin/bash

# Maitrix Auto Bot Startup Script
# Created by NT Exhaust

echo "🚀 Maitrix Auto Bot - NT Exhaust"
echo "=================================="
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js tidak ditemukan. <PERSON>lakan install Node.js terlebih dahulu."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm tidak ditemukan. <PERSON><PERSON><PERSON> install npm terlebih dahulu."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ File .env tidak ditemukan!"
    echo "📝 Silakan buat file .env dengan format:"
    echo ""
    echo "PRIVATE_KEY=your_private_key_1"
    echo "PRIVATE_KEY_2=your_private_key_2"
    echo "RPC_URL=https://sepolia-rollup.arbitrum.io/rpc"
    echo "AUTO_MODE=true"
    echo ""
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Gagal install dependencies!"
        exit 1
    fi
    echo "✅ Dependencies installed successfully!"
    echo ""
fi

# Show menu
echo "🎮 Pilih mode operasi:"
echo "1. Auto Mode (24H Loop) - Recommended"
echo "2. Manual Mode (CLI Interface)"
echo "3. Exit"
echo ""
read -p "Pilih opsi (1-3): " choice

case $choice in
    1)
        echo ""
        echo "🚀 Starting Auto Mode (24H Loop)..."
        echo "⚠️  Bot akan berjalan otomatis selama 24 jam"
        echo "⚠️  Tekan Ctrl+C untuk menghentikan"
        echo ""
        sleep 3
        AUTO_MODE=true node index.js
        ;;
    2)
        echo ""
        echo "🎮 Starting Manual Mode..."
        echo "💡 Gunakan menu untuk kontrol manual"
        echo ""
        sleep 2
        AUTO_MODE=false node index.js
        ;;
    3)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Pilihan tidak valid!"
        exit 1
        ;;
esac
