# 🚀 Maitrix Auto Bot - NT Exhaust

Bot otomatis untuk Maitrix Testnet yang menjalankan claim faucet, mint, dan stake secara otomatis dengan 2 wallet dalam loop 24 jam tanpa interaksi manual.

## ✨ Fitur Utama

### 🔄 **Auto Mode 24 Jam**
- **Otomatis 100%**: <PERSON><PERSON><PERSON> jalan, be<PERSON><PERSON><PERSON> sendiri selama 24 jam
- **Multi Wallet**: Support 2 wallet sekaligus
- **Siklus <PERSON>kap**: <PERSON><PERSON><PERSON> Faucet → Mint → Stake → Switch Wallet → Repeat
- **No Manual Input**: Semua menggunakan full balance otomatis

### 💰 **Operasi Otomatis**
- **Auto Claim Faucet**: Claim semua faucet (ATH, USDe, LULUSD, Ai16Z, Virtual, Vana, USD1, OG)
- **Auto Mint Full Balance**: Mint semua token dengan 100% balance yang tersedia
- **Auto Stake Full Balance**: Stake semua token dengan 100% balance yang tersedia
- **Auto Switch Wallet**: Berganti wallet otomatis setelah siklus selesai

### 🎯 **Token Support**
- **Mintable Tokens**: AUSD, VUSD, VANAUSD, AZUSD, OUSD
- **Stakeable Tokens**: AZUSD, AUSD, VANAUSD, VUSD, USDE, LULUSD, OUSD, USD1
- **Faucet Tokens**: ATH, USDe, LULUSD, Ai16Z, Virtual, Vana, USD1, OG

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Setup Environment
Edit file `.env`:
```env
PRIVATE_KEY=your_private_key_1
PRIVATE_KEY_2=your_private_key_2
RPC_URL=https://sepolia-rollup.arbitrum.io/rpc
AUTO_MODE=true
# ... token addresses (sudah ada)
```

### 3. Jalankan Bot

#### Mode Otomatis (24 Jam Loop)
```bash
npm start
```
atau
```bash
npm run auto
```

#### Mode Manual (CLI Interface)
```bash
npm run manual
```

## 🔄 Cara Kerja Auto Mode

1. **Startup**: Bot mendeteksi 2 wallet dari PRIVATE_KEY dan PRIVATE_KEY_2
2. **Auto Start**: Jika AUTO_MODE=true, bot otomatis mulai dalam 5 detik
3. **Siklus Per Wallet**:
   - 📥 Claim semua faucet (8 token)
   - ⏳ Tunggu 5 detik
   - 🏭 Mint semua token dengan full balance
   - ⏳ Tunggu 5 detik
   - 🥩 Stake semua token dengan full balance
4. **Switch Wallet**: Beralih ke wallet berikutnya
5. **Repeat**: Ulangi siklus untuk semua wallet
6. **Loop**: Tunggu 5 menit, lalu mulai siklus baru
7. **Duration**: Berjalan selama 24 jam penuh

## 📊 Monitoring

### Real-time Logs
- Status setiap transaksi
- Progress siklus dan waktu tersisa
- Balance update otomatis
- Error handling dan retry

### Transaction Queue
- Antrian transaksi real-time
- Status: queued → processing → completed
- Transaction ID tracking

## ⚙️ Konfigurasi

### Environment Variables
```env
# Required
PRIVATE_KEY=wallet_1_private_key
PRIVATE_KEY_2=wallet_2_private_key
RPC_URL=https://sepolia-rollup.arbitrum.io/rpc

# Optional
AUTO_MODE=true  # true = auto start, false = manual
```

### Auto Mode Settings
- **Cycle Interval**: 5 menit antar siklus
- **Wallet Switch Delay**: 10 detik
- **Operation Delay**: 5 detik antar operasi
- **Total Duration**: 24 jam

## 🎮 Manual Mode

Jika AUTO_MODE=false atau menggunakan `npm run manual`, tersedia menu:

- **Start Auto Mode (24H Loop)** - Mulai mode otomatis
- **Stop Auto Mode** - Hentikan mode otomatis
- **Auto Mint All (Full Balance)** - Mint semua token
- **Auto Stake All (Full Balance)** - Stake semua token
- **Switch Wallet** - Ganti wallet manual
- **Auto Claim Faucet** - Claim semua faucet
- **Transaction Queue** - Lihat antrian transaksi

## 🛡️ Safety Features

- **Error Handling**: Auto retry dan skip jika error
- **Balance Check**: Cek balance sebelum transaksi
- **Approval Management**: Auto approve token jika diperlukan
- **Queue System**: Transaksi terorganisir dalam antrian
- **Graceful Stop**: Bisa dihentikan kapan saja dengan Ctrl+C

## 📈 Performance

- **Efficiency**: Menggunakan full balance untuk maksimal profit
- **Speed**: Optimized delay antar transaksi
- **Reliability**: Robust error handling
- **Scalability**: Support multiple wallets

## 🔧 Troubleshooting

### Bot Tidak Start Otomatis
- Pastikan `AUTO_MODE=true` di .env
- Cek PRIVATE_KEY dan PRIVATE_KEY_2 valid
- Pastikan RPC_URL accessible

### Transaksi Gagal
- Cek balance ETH untuk gas fee
- Pastikan token balance cukup
- Lihat logs untuk error detail

### Faucet Claim Gagal
- Normal jika sudah claim hari ini
- Bot akan skip dan lanjut ke operasi berikutnya

## 📞 Support

- **Telegram**: @NTExhaust
- **Tutorial**: https://t.me/NTExhaust

## ⚠️ Disclaimer

- Gunakan dengan risiko sendiri
- Pastikan private key aman
- Test dengan amount kecil dulu
- Bot ini untuk testnet, bukan mainnet

---

**🎯 Hasil yang Diharapkan:**
- Claim faucet otomatis setiap hari
- Mint dan stake maksimal dengan full balance
- Operasi 24/7 tanpa intervensi manual
- Profit maksimal dari 2 wallet sekaligus
